<template>
  <div class="create-database-page">
    <router-view v-if="$route.params.id"></router-view>
    <div class="page-content" v-else>
      <div
        v-loading="loading"
        class="loading-container"
        element-loading-text="加载数据库中..."
      >
        <div class="group_2 flex-col">
          <!-- 页面头部 -->
          <div class="box_19 flex-row">
            <span class="text_12">数据库</span>
            <div class="text-wrapper_2">
              <span class="text_13">剩余内置数据库条目：37.5mb</span>
              <span class="text_14">&nbsp;</span>
              <span class="text_15">扩容</span>
            </div>
            <div class="group_3 flex-row" @click="handleCreateDatabase">
              <i class="el-icon-plus thumbnail_21"></i>
              <span class="text_16">新建数据库</span>
            </div>
          </div>

          <!-- 搜索框 -->
          <div class="box_20 flex-row">
            <i class="el-icon-search thumbnail_22"></i>
            <el-input
              v-model="searchKeyword"
              placeholder="搜索数据库"
              class="search-input"
              @input="handleSearch"
              clearable
            />
          </div>

          <!-- 筛选标签 -->
          <div class="box_21 flex-row">
            <div
              :class="['text-wrapper_3', 'flex-col', { active: currentFilter === 'all' }]"
              @click="handleFilterChange('all')"
            >
              <span class="text_18">全部</span>
            </div>
            <div
              :class="['text-wrapper_4', 'flex-col', { active: currentFilter === 'remote' }]"
              @click="handleFilterChange('remote')"
            >
              <span class="text_19">远程数据库</span>
            </div>
            <div
              :class="['text-wrapper_5', 'flex-col', { active: currentFilter === 'internal' }]"
              @click="handleFilterChange('internal')"
            >
              <span class="text_20">内置数据库</span>
            </div>
          </div>

          <!-- 数据库卡片容器 -->
          <div class="box_22 flex-row justify-between">
            <div class="grid_1 flex-row">
            <!-- 数据库卡片列表 -->
            <div
              v-for="(database, index) in filteredDatabaseList"
              :key="database.id"
              class="database-card block_3 flex-col"
              :style="{ '--card-index': index }"
              @click="handleSettingClick(database)"
            >
              <div class="block_4 flex-row">
                <div class="label_3">
                  <i
                    :class="[
                      'database-icon',
                      database.dbType === 'internal'
                        ? 'el-icon-coin'
                        : 'el-icon-connection',
                    ]"
                  ></i>
                </div>
                <div class="text-wrapper_6 flex-col justify-between">
                  <h3 class="text_21 database-name">{{ database.name }}</h3>
                  <el-tooltip
                    :content="database.description"
                    placement="top"
                    :disabled="database.description.length <= 60"
                  >
                    <p class="text_22 database-desc">{{ database.description }}</p>
                  </el-tooltip>
                </div>
                <div class="text-wrapper_7 flex-col">
                  <span class="text_23 status-tag">运行中</span>
                </div>
              </div>

              <div class="block_5 flex-row justify-between">
                <div
                  :class="[
                    'text-wrapper_8',
                    'flex-col',
                    database.dbType === 'internal' ? 'internal-type' : 'remote-type'
                  ]"
                >
                  <span class="text_24 db-type-tag">
                    {{ database.dbType === "internal" ? "内置数据库" : "远程数据库" }}
                  </span>
                </div>
                <span class="text_25 app-reference">应用引用 3</span>
              </div>

              <div class="image_8"></div>

              <div class="block_6 flex-row">
                <span class="text_26 update-time">最近更新 2025-07-10 14:20:30</span>
                <div class="text-wrapper_9 flex-col">
                  <span class="text_27 edit-btn">编辑</span>
                </div>
                <div class="label_4 action-icon" @click.stop="handleSettingClick(database)">
                  <i class="el-icon-setting"></i>
                </div>
              </div>
            </div>
            </div>
            <div class="box_35 flex-col"></div>
          </div>


        </div>
      </div>
    </div>

    <!-- 创建数据库弹窗 -->
    <el-dialog
      title="新建数据库"
      :visible.sync="createDialogVisible"
      :close-on-click-modal="false"
      width="600px"
      append-to-body
      @close="handleDialogClose"
    >
      <!-- 弹框右上角的查看文档按钮 -->
      <div class="dialog-header-actions">
        <el-button type="text" @click="handleViewDocs" class="view-docs-btn">
          <i class="el-icon-document"></i>
          查看文档
        </el-button>
      </div>

      <!-- 数据库类型选择 -->
      <div class="database-type-select">
        <div
          :class="['type-card', { active: form.type === 'internal' }]"
          @click="form.type = 'internal'"
        >
          <el-tooltip
            content="使用系统内置的数据库，无需额外配置，支持增删改查操作"
            placement="top"
            class="tooltip-wrapper"
            @click.native.stop
          >
            <i class="el-icon-question tooltip-icon"></i>
          </el-tooltip>
          <i class="el-icon-coin"></i>
          <span>内置数据库</span>
        </div>
        <div
          :class="['type-card', { active: form.type === 'remote', disabled: true }]"
          @click="handleRemoteClick"
        >
          <el-tooltip
            content="连接外部数据库，需要配置连接信息，仅支持查询操作（暂未开放）"
            placement="top"
            class="tooltip-wrapper"
            @click.native.stop
          >
            <i class="el-icon-question tooltip-icon"></i>
          </el-tooltip>
          <i class="el-icon-connection"></i>
          <span>远程数据库</span>
          <div class="disabled-overlay">敬请期待</div>
        </div>
      </div>

      <!-- 基础信息表单 -->
      <div class="form-section">
        <div class="section-title">基础信息</div>
        <el-form
          :model="form"
          ref="createForm"
          :rules="rules"
          label-position="top"
        >
          <el-form-item label="数据库名称" prop="name" class="form-item">
            <el-input
              v-model="form.name"
              placeholder="请填写数据库名称"
              maxlength="100"
              show-word-limit
            ></el-input>
          </el-form-item>

          <el-form-item label="数据库描述" prop="description" class="form-item">
            <el-input
              type="textarea"
              v-model="form.description"
              placeholder="请详细描述您的数据库使用场景"
              :rows="4"
              maxlength="1000"
              show-word-limit
            ></el-input>
          </el-form-item>

          <el-form-item label="权限设置" prop="permissions" class="form-item">
            <el-checkbox-group v-model="form.permissions">
              <el-checkbox :label="1" :disabled="form.type === 'remote'"
                >查询</el-checkbox
              >
              <el-checkbox v-if="form.type === 'internal'" :label="2"
                >新增</el-checkbox
              >
              <el-checkbox v-if="form.type === 'internal'" :label="4"
                >编辑</el-checkbox
              >
              <el-checkbox v-if="form.type === 'internal'" :label="8"
                >删除</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 连接信息表单，仅在选择远程数据库时显示 -->
      <div v-if="form.type === 'remote'" class="form-section">
        <div class="section-title">连接信息</div>
        <el-form
          :model="form"
          ref="connectionForm"
          :rules="connectionRules"
          label-position="top"
        >
          <el-form-item label="数据库类型" prop="dbType" class="form-item">
            <el-select
              v-model="form.dbType"
              placeholder="请选择数据库类型"
              class="full-width"
            >
              <el-option label="MySQL" value="mysql">
                <img src="mysql-logo.png" class="db-type-icon" alt="MySQL" />
                <span>MySQL</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="主机" prop="host" class="form-item">
            <el-input v-model="form.host" placeholder="请输入"></el-input>
          </el-form-item>

          <el-form-item label="端口" prop="port" class="form-item">
            <el-input v-model="form.port" placeholder="请输入"></el-input>
          </el-form-item>

          <el-form-item label="用户名" prop="username" class="form-item">
            <el-input v-model="form.username" placeholder="请输入"></el-input>
          </el-form-item>

          <el-form-item label="密码" prop="password" class="form-item">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入"
              show-password
            ></el-input>
          </el-form-item>

          <el-form-item label="数据库名" prop="database" class="form-item">
            <el-input v-model="form.database" placeholder="请输入"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleCreateConfirm"
          :loading="submitting"
          >{{ form.type === "remote" ? "下一步" : "确定" }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from "@/api/request"

export default {
  name: "CreateDatabasePage",
  data() {
    return {
      loading: false, // 页面加载状态
      createDialogVisible: false,
      submitting: false, // 提交状态
      searchKeyword: '', // 搜索关键词
      currentFilter: 'all', // 当前筛选类型：all, remote, internal
      searchTimer: null, // 搜索防抖定时器
      form: {
        type: "internal",
        name: "",
        description: "",
        permissions: [1, 2],
        dbType: "mysql",
        host: "",
        port: "3306",
        username: "",
        password: "",
        database: "",
      },
      rules: {
        name: [
          { required: true, message: "请输入数据库名称", trigger: "blur" },
          { min: 1, max: 100, message: "长度在 1 到 100 个字符", trigger: "blur" },
        ],
        description: [
          { required: true, message: "请输入数据库描述", trigger: "blur" },
          { min: 1, max: 1000, message: "长度在 1 到 1000 个字符", trigger: "blur" },
        ],
      },
      connectionRules: {
        dbType: [
          { required: true, message: "请选择数据库类型", trigger: "change" },
        ],
        host: [{ required: true, message: "请输入主机地址", trigger: "blur" }],
        port: [
          { required: true, message: "请输入端口号", trigger: "blur" },
          { pattern: /^\d+$/, message: "端口号必须为数字", trigger: "blur" },
        ],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        database: [
          { required: true, message: "请输入数据库名", trigger: "blur" },
        ],
      },
      databaseList: [],
    };
  },
  computed: {
    // 筛选后的数据库列表
    filteredDatabaseList() {
      let filtered = this.databaseList;

      // 根据类型筛选
      if (this.currentFilter !== 'all') {
        filtered = filtered.filter(db => db.dbType === this.currentFilter);
      }

      // 根据搜索关键词筛选
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase().trim();
        filtered = filtered.filter(db =>
          db.name.toLowerCase().includes(keyword) ||
          db.description.toLowerCase().includes(keyword)
        );
      }

      return filtered;
    }
  },
  watch: {
    "form.type": {
      handler(newType) {
        if (newType === "remote") {
          this.form.permissions = [1];
                  } else {
            this.form.permissions = [1, 2];
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.fetchDatabaseList();
  },
  beforeDestroy() {
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  },
  methods: {
    // 获取数据库列表
    async fetchDatabaseList() {
      this.loading = true;
      try {
        const response = await api.schemaStore.getDatabases({
          skipCount: 0,
          maxResultCount: 100,
        });
        console.log(response);

        if (response && response.data && response.data.items) {
          // 映射接口返回的数据到组件需要的格式
          this.databaseList = response.data.items.map((item) => ({
            id: item.id,
            name: item.databaseName,
            description: item.description,
            dbType: item.databaseType?.value === 1 ? "internal" : "remote",
          }));
        } else {
          this.databaseList = [];
        }
      } catch (error) {
        console.error("获取数据库列表失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 创建数据库
    async createDatabase() {
      if(this.form.permissions.length < 1){
        this.$showFriendlyError(null, "请至少选择一个权限！");
        return;
      }
      this.submitting = true;
      try {
        // 构建请求参数
        const requestData = {
          databaseName: this.form.name,
          description: this.form.description,
          databaseType: this.form.type === "internal" ? 1 : 2, // 1=内置，2=远程
          permissions: this.form.permissions, // 直接使用数字数组
        };

        // 如果是远程数据库，添加连接信息
        if (this.form.type === "remote") {
          requestData.connectionString = this.buildConnectionString();
        }

        const response = await api.schemaStore.createDatabase(requestData);

        if (response && response.isSuccess) {
          this.$message.success("创建成功");
          this.createDialogVisible = false;
          this.resetForm();
          // 刷新数据库列表
          this.fetchDatabaseList();
        }
      } catch (error) {
        console.error("创建数据库失败:", error);
      } finally {
        this.submitting = false;
      }
    },



    // 构建连接字符串（远程数据库）
    buildConnectionString() {
      const { dbType, host, port, username, password, database } = this.form;
      if (dbType === "mysql") {
        return `Server=${host};Port=${port};Database=${database};Uid=${username};Pwd=${password};`;
      }
      // 可以根据需要支持其他数据库类型
      return "";
    },

    // 重置表单
    resetForm() {
      this.$refs.createForm?.resetFields();
      this.$refs.connectionForm?.resetFields();
      // 手动重置一些字段
      this.form.type = "internal";
      this.form.permissions = [1, 2];
      this.form.port = "3306";
    },

    handleCreateDatabase() {
      this.createDialogVisible = true;
    },
    handleSettingClick(database) {
      this.$router
        .push({
          name: "DatabaseSetting",
          params: { id: database.id },
        })
        .catch((err) => {
          if (err.name !== "NavigationDuplicated") {
            throw err;
          }
        });
    },
    handleDialogClose() {
      this.createDialogVisible = false;
      this.resetForm();
    },
    handleCreateConfirm() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          // 如果是远程数据库，还需要验证连接信息
          if (this.form.type === "remote") {
            this.$refs.connectionForm.validate((connectionValid) => {
              if (connectionValid) {
                this.createDatabase();
              }
            });
          } else {
            this.createDatabase();
          }
        }
      });
    },
    handleViewDocs() {
      this.$message.info("为了给您更完美的体验，我们正在做最后的冲刺，很快解锁");
    },
    handleRemoteClick() {
      this.$message.warning("远程数据库功能暂未开放，敬请期待！");
    },

    // 处理搜索（防抖）
    handleSearch() {
      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，300ms后执行搜索
      this.searchTimer = setTimeout(() => {
        console.log('搜索关键词:', this.searchKeyword);
        // 搜索逻辑在计算属性中自动处理
      }, 300);
    },

    // 处理筛选类型切换
    handleFilterChange(filterType) {
      this.currentFilter = filterType;
      console.log('切换筛选类型:', filterType);
    },
  },
};
</script>

<style lang="scss" scoped>
.create-database-page {
  background-color: #f5f7fa;
  height: 100vh;
  border-radius: 12px 0 0 12px;
  box-sizing: border-box;
  overflow-y: auto;

  // Flex布局通用类
  .flex-row {
    display: flex;
    flex-direction: row;
  }

  .flex-col {
    display: flex;
    flex-direction: column;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .align-center {
    align-items: center;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 10px;
      font-weight: 500;
    }

    .page-desc {
      color: #606266;
      font-size: 14px;
      margin: 0;
    }
  }

  .page-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-card + .el-card {
      margin-top: 20px;
    }
  }

  .loading-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .group_2 {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    width: 100%;
    flex: 1;
    border: 0.5px solid rgba(235, 236, 241, 1);
    padding: 0 0 40px 0;
    display: flex;
    flex-direction: column;
  }

  .box_19 {
    width: calc(100% - 80px);
    height: 36px;
    margin: 30px 0 0 40px;
    display: flex;
    align-items: flex-start;
    position: relative;
  }

  .text_12 {
    width: 72px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 24px;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
    line-height: 33px;
    margin-top: 2px;
  }

  .text-wrapper_2 {
    width: 228px;
    height: 20px;
    overflow-wrap: break-word;
    font-size: 0;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 9px 0 0 16px;
  }

  .text_13 {
    width: 228px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(102, 102, 102, 1);
    font-size: 14px;
    text-transform: uppercase;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
  }

  .text_14 {
    width: 228px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(0, 0, 0, 1);
    font-size: 14px;
    text-transform: uppercase;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
  }

  .text_15 {
    width: 228px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(37, 109, 255, 1);
    font-size: 14px;
    text-transform: uppercase;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    cursor: pointer;
  }

  .group_3 {
    background-color: rgba(37, 109, 255, 1);
    border-radius: 8px;
    width: 116px;
    height: 36px;
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(33, 99, 230, 1);
    }
  }

  .thumbnail_21 {
    width: 10px;
    height: 10px;
    margin-right: 10px;
    color: rgba(255, 255, 255, 1);
    font-size: 10px;
  }

  .text_16 {
    width: 70px;
    height: 20px;
    overflow-wrap: break-word;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    text-align: left;
    white-space: nowrap;
    line-height: 20px;
    margin: 0;
  }

  .box_20 {
    background-color: rgba(244, 246, 248, 1);
    border-radius: 8px;
    width: 240px;
    height: 36px;
    margin: 23px 0 0 40px;
    display: flex;
    align-items: center;
    padding: 0 13px;
    position: relative;
  }

  .thumbnail_22 {
    width: 15px;
    height: 16px;
    margin-right: 12px;
    color: rgba(186, 186, 186, 1);
    font-size: 15px;
    z-index: 2;
  }

  .search-input {
    flex: 1;

    :deep(.el-input__inner) {
      border: none;
      background: transparent;
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      padding: 0;
      height: 20px;
      line-height: 20px;

      &::placeholder {
        color: rgba(186, 186, 186, 1);
      }
    }

    :deep(.el-input__suffix) {
      right: 0;
    }
  }

  .box_21 {
    background-color: rgba(244, 246, 248, 1);
    border-radius: 8px;
    width: 232px;
    height: 40px;
    margin: 24px 0 0 40px;
    display: flex;
    align-items: center;
    padding: 0 4px;
  }

  .text-wrapper_3,
  .text-wrapper_4,
  .text-wrapper_5 {
    height: 30px;
    border-radius: 4px;
    margin: 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .text-wrapper_3 {
    width: 44px;

    &.active {
      background: rgba(255, 255, 255, 1);

      .text_18 {
        color: rgba(37, 109, 255, 1);
      }
    }

    &:not(.active) {
      background: transparent;

      .text_18 {
        color: rgba(0, 0, 0, 1);
      }
    }

    .text_18 {
      font-size: 14px;
      font-weight: normal;
      margin: 0;
    }
  }

  .text-wrapper_4,
  .text-wrapper_5 {
    width: 86px;

    &.active {
      background: rgba(255, 255, 255, 1);

      .text_19,
      .text_20 {
        color: rgba(37, 109, 255, 1);
      }
    }

    &:not(.active) {
      background: transparent;

      .text_19,
      .text_20 {
        color: rgba(0, 0, 0, 1);
      }
    }

    .text_19,
    .text_20 {
      font-size: 14px;
      font-weight: normal;
      margin: 0;
    }
  }

  .box_22 {
    width: calc(100% - 80px);
    margin: 32px 0 0 40px;
    flex: 1;
    display: flex;
    flex-direction: row;
    position: relative;
  }

  .grid_1 {
    width: calc(100% - 41px); /* 减去滚动条宽度和间距 */
    min-height: 346px;
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
  }

  .database-card.block_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    width: calc(33.333% - 14px); /* 三等分减去间距 */
    min-width: 320px; /* 最小宽度保证内容不会太挤 */
    max-width: 511px; /* 最大宽度与demo保持一致 */
    height: 161px;
    border: 0.5px solid rgba(235, 236, 241, 1);
    justify-content: flex-start;
    margin: 0 20px 24px 0;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    animation: fadeInUp 0.5s ease forwards;
    animation-delay: calc(var(--card-index, 0) * 0.1s);
    opacity: 0;
    transform-origin: center;

    &:nth-child(3n) {
      margin-right: 0;
    }

    &:nth-last-child(-n + 3) {
      margin-bottom: 0;
    }

    &:hover {
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: scale(0.98);
    }

    .block_4 {
      width: calc(100% - 32px);
      height: 50px;
      margin: 12px 0 0 16px;
    }

    .label_3 {
      width: 44px;
      height: 44px;
      margin-top: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      background: var(--el-color-primary-light-9);

      .database-icon {
        font-size: 24px;
        color: var(--el-color-primary);
      }
    }

    .text-wrapper_6 {
      width: calc(100% - 120px); /* 减去图标和状态标签的宽度 */
      height: 46px;
      margin: 4px 0 0 12px;
    }

    .text_21.database-name {
      width: auto;
      max-width: 100%;
      height: 22px;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 16px;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 22px;
      margin: 0;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .text_22.database-desc {
      width: 100%;
      height: 20px;
      overflow-wrap: break-word;
      color: rgba(136, 136, 136, 1);
      font-size: 12px;
      text-align: left;
      white-space: nowrap;
      line-height: 20px;
      margin: 4px 0 0 0;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .text-wrapper_7 {
      background-color: rgba(244, 246, 248, 1);
      border-radius: 4px;
      height: 20px;
      margin-left: 123px;
      width: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .text_23.status-tag {
      width: 36px;
      height: 18px;
      overflow-wrap: break-word;
      color: rgba(153, 153, 153, 1);
      font-size: 12px;
      text-align: center;
      white-space: nowrap;
      line-height: 18px;
      margin: 0;
    }

    .block_5 {
      width: 141px;
      height: 20px;
      margin: 8px 0 0 72px;
    }

    .text-wrapper_8 {
      border-radius: 4px;
      height: 20px;
      width: 70px;
      display: flex;
      align-items: center;
      justify-content: center;

      // 根据数据库类型设置不同的背景色
      &.internal-type {
        background-color: rgba(237, 243, 254, 1);

        .text_24.db-type-tag {
          color: rgba(37, 109, 255, 1);
        }
      }

      &.remote-type {
        background-color: rgba(221, 251, 254, 1);

        .text_24.db-type-tag {
          color: rgba(0, 173, 190, 1);
        }
      }
    }

    .text_24.db-type-tag {
      width: 60px;
      height: 18px;
      overflow-wrap: break-word;
      font-size: 12px;
      text-align: center;
      white-space: nowrap;
      line-height: 18px;
      margin: 0;
    }

    .text_25.app-reference {
      width: 60px;
      height: 18px;
      overflow-wrap: break-word;
      color: rgba(186, 186, 186, 1);
      font-size: 12px;
      text-align: left;
      white-space: nowrap;
      line-height: 18px;
      margin: 1px 0 0 0;
    }

    .image_8 {
      width: calc(100% - 32px);
      height: 2px;
      margin: 15px 0 0 16px;
      background-color: rgba(235, 236, 241, 1);
    }

    .block_6 {
      width: calc(100% - 30px);
      height: 30px;
      margin: 12px 0 12px 15px;
      display: flex;
      align-items: center;
    }

    .text_26.update-time {
      width: 173px;
      height: 20px;
      overflow-wrap: break-word;
      color: rgba(186, 186, 186, 1);
      font-size: 12px;
      text-align: left;
      white-space: nowrap;
      line-height: 20px;
      margin: 0;
    }

    .text-wrapper_9 {
      background-color: rgba(244, 246, 248, 1);
      border-radius: 4px;
      height: 30px;
      margin-left: 203px;
      width: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: rgba(230, 235, 240, 1);
      }
    }

    .text_27.edit-btn {
      width: 28px;
      height: 20px;
      overflow-wrap: break-word;
      color: rgba(0, 0, 0, 1);
      font-size: 14px;
      text-align: center;
      white-space: nowrap;
      line-height: 20px;
      margin: 0;
    }

    .label_4.action-icon {
      width: 44px;
      height: 30px;
      margin-left: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        opacity: 0.8;
      }

      i {
        font-size: 16px;
        color: #666;
      }
    }
  }

  .create-card.block_3 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 8px;
    width: 511px;
    height: 161px;
    border: 0.5px solid rgba(235, 236, 241, 1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    animation: fadeInUp 0.5s ease forwards;
    animation-delay: 0.3s;
    opacity: 0;
    margin: 0 20px 24px 0;

    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
      background: var(--el-color-primary-light-9);

      i,
      span {
        color: var(--el-color-primary);
      }

      i {
        transform: rotate(90deg);
      }
    }

    &:active {
      transform: scale(0.98);
    }

    i {
      font-size: 32px;
      margin-bottom: 12px;
      color: #9ca3af;
      transition: all 0.3s ease;
    }

    span {
      font-size: 16px;
      font-weight: 500;
      color: #9ca3af;
      transition: color 0.3s ease;
    }
  }

  .box_35 {
    background-color: rgba(223, 223, 223, 1);
    border-radius: 3px;
    width: 8px;
    height: 100px;
    margin-top: 123px;
    margin-left: 25px;
    flex-shrink: 0;
  }

}

.loading-container {
  min-height: 300px;
}

.form-item {
  margin-bottom: 24px;

  :deep(.el-form-item__label) {
    padding-bottom: 8px;
    line-height: 1;
    color: #606266;
  }
}

.dialog-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
  font-size: 14px;

  i {
    color: var(--el-color-primary);
  }
}

:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    padding: 20px 20px 10px;
    margin-right: 0;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
  }

  .el-dialog__body {
    padding: 24px 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #f0f0f0;
  }
}

.dialog-header-actions {
  position: absolute;
  top: 20px;
  right: 60px;
  z-index: 1;

  .view-docs-btn {
    color: #409eff;
    padding: 8px 12px;
    font-size: 14px;

    &:hover {
      background-color: #ecf5ff;
      border-radius: 4px;
    }

    i {
      margin-right: 4px;
    }
  }
}

.database-type-select {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;

  .type-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);
    }

    &.active {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary-light-9);

      i,
      span {
        color: var(--el-color-primary);
      }
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
      position: relative;

      &:hover {
        border-color: #e4e7ed;
        background: #fff;
      }
    }

    .disabled-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #909399;
      z-index: 1;
    }

    .tooltip-wrapper {
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 18px;
    }

    i {
      font-size: 32px;
      margin-bottom: 12px;
      color: #909399;
    }

    span {
      font-size: 14px;
      color: #606266;
    }
  }
}

.form-section {
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 20px;
  }
}

.full-width {
  width: 100%;
}

.db-type-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  align-items: center;
}
</style>
